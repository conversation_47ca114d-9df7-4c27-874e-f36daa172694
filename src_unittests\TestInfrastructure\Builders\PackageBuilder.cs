using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;

namespace RazeWinComTr.Tests.TestInfrastructure.Builders
{
    /// <summary>
    /// Builder pattern for creating Package entities in tests.
    /// Provides predefined package types and custom configuration options.
    /// 
    /// Usage Example:
    /// var package = PackageBuilder.Bronze().Build();
    /// var customPackage = new PackageBuilder()
    ///     .WithName("Custom")
    ///     .WithPrice(500)
    ///     .WithRewardPercentages(new[] { 10m, 5m })
    ///     .Build();
    /// </summary>
    public class PackageBuilder
    {
        private Package _package;
        private List<PackageRewardPercentage> _rewardPercentages;

        public PackageBuilder()
        {
            _package = new Package
            {
                Id = 1,
                Name = "Test Package",
                Price = 100,
                IsActive = 1,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };
            _rewardPercentages = new List<PackageRewardPercentage>();
        }

        /// <summary>
        /// Sets the package ID.
        /// </summary>
        public PackageBuilder WithId(int id)
        {
            _package.Id = id;
            return this;
        }

        /// <summary>
        /// Sets the package name.
        /// </summary>
        public PackageBuilder WithName(string name)
        {
            _package.Name = name;
            return this;
        }

        /// <summary>
        /// Sets the package price.
        /// </summary>
        public PackageBuilder WithPrice(decimal price)
        {
            _package.Price = price;
            return this;
        }

        /// <summary>
        /// Sets the package description.
        /// </summary>
        public PackageBuilder WithDescription(string description)
        {
            _package.Description = description;
            return this;
        }

        /// <summary>
        /// Sets the invite limit for the package.
        /// </summary>
        public PackageBuilder WithInviteLimit(int? inviteLimit)
        {
            _package.InviteLimit = inviteLimit;
            return this;
        }

        /// <summary>
        /// Sets the package as active.
        /// </summary>
        public PackageBuilder AsActive()
        {
            _package.IsActive = 1;
            return this;
        }

        /// <summary>
        /// Sets the package as inactive.
        /// </summary>
        public PackageBuilder AsInactive()
        {
            _package.IsActive = 0;
            return this;
        }

        /// <summary>
        /// Sets reward percentages for different referral levels.
        /// </summary>
        /// <param name="percentages">Array of percentages for levels 1, 2, 3, etc.</param>
        public PackageBuilder WithRewardPercentages(params decimal[] percentages)
        {
            _rewardPercentages.Clear();
            
            for (int i = 0; i < percentages.Length; i++)
            {
                _rewardPercentages.Add(new PackageRewardPercentage
                {
                    PackageId = _package.Id,
                    Level = i + 1,
                    Percentage = percentages[i],
                    IsActive = 1,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow
                });
            }
            
            return this;
        }

        /// <summary>
        /// Builds and returns the configured Package entity with reward percentages.
        /// </summary>
        public (Package Package, List<PackageRewardPercentage> RewardPercentages) Build()
        {
            return (_package, _rewardPercentages);
        }

        /// <summary>
        /// Builds and returns only the Package entity.
        /// </summary>
        public Package BuildPackageOnly()
        {
            return _package;
        }

        /// <summary>
        /// Creates a Bronze package with standard configuration.
        /// Bronze: 1 level, 5% reward
        /// </summary>
        public static PackageBuilder Bronze()
        {
            return new PackageBuilder()
                .WithId(1)
                .WithName("Bronze")
                .WithPrice(100)
                .WithDescription("Bronze package with 1 level referral")
                .WithRewardPercentages(5m);
        }

        /// <summary>
        /// Creates a Silver package with standard configuration.
        /// Silver: 2 levels, 10% and 5% rewards
        /// </summary>
        public static PackageBuilder Silver()
        {
            return new PackageBuilder()
                .WithId(2)
                .WithName("Silver")
                .WithPrice(250)
                .WithDescription("Silver package with 2 level referral")
                .WithRewardPercentages(10m, 5m);
        }

        /// <summary>
        /// Creates a Gold package with standard configuration.
        /// Gold: 3 levels, 15%, 10%, and 5% rewards
        /// </summary>
        public static PackageBuilder Gold()
        {
            return new PackageBuilder()
                .WithId(3)
                .WithName("Gold")
                .WithPrice(500)
                .WithDescription("Gold package with 3 level referral")
                .WithRewardPercentages(15m, 10m, 5m);
        }

        /// <summary>
        /// Creates a Platinum package with standard configuration.
        /// Platinum: 4 levels, 20%, 15%, 10%, and 5% rewards
        /// </summary>
        public static PackageBuilder Platinum()
        {
            return new PackageBuilder()
                .WithId(4)
                .WithName("Platinum")
                .WithPrice(1000)
                .WithDescription("Platinum package with 4 level referral")
                .WithRewardPercentages(20m, 15m, 10m, 5m);
        }

        /// <summary>
        /// Creates all standard packages (Bronze, Silver, Gold, Platinum).
        /// </summary>
        public static List<(Package Package, List<PackageRewardPercentage> RewardPercentages)> AllStandardPackages()
        {
            return new List<(Package, List<PackageRewardPercentage>)>
            {
                Bronze().Build(),
                Silver().Build(),
                Gold().Build(),
                Platinum().Build()
            };
        }

        /// <summary>
        /// Creates a package with no reward levels (for testing edge cases).
        /// </summary>
        public static PackageBuilder NoRewards()
        {
            return new PackageBuilder()
                .WithName("No Rewards")
                .WithPrice(50)
                .WithDescription("Package with no referral rewards");
        }

        /// <summary>
        /// Creates a package with unlimited invite limit.
        /// </summary>
        public static PackageBuilder UnlimitedInvites()
        {
            return Bronze()
                .WithInviteLimit(null);
        }

        /// <summary>
        /// Creates a package with limited invites.
        /// </summary>
        public static PackageBuilder LimitedInvites(int limit)
        {
            return Bronze()
                .WithInviteLimit(limit);
        }
    }
}
