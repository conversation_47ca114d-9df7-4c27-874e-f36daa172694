using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace RazeWinComTr.Tests.EdgeCases
{
    /// <summary>
    /// Tests for edge cases in the ReferralRewardService.
    /// These tests verify the behavior of the service in unusual or boundary conditions,
    /// such as maximum referral chain length, zero amounts, and expired packages.
    /// </summary>
    public class ReferralRewardEdgeCaseTests : TestBase
    {
        /// <summary>
        /// Tests that rewards are distributed correctly when a user has the maximum number of referrers.
        /// This test creates a referral chain with the maximum allowed number of levels (10) and verifies
        /// that rewards are distributed correctly to all eligible referrers.
        ///
        /// The test verifies:
        /// - Rewards are distributed to all 10 levels
        /// - Each level receives the correct percentage and amount
        /// - The total distributed amount is correct
        /// - Trade records are created for each reward
        /// </summary>
        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithMaximumReferralChain_DistributesRewardsCorrectly()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithMaximumReferralChain_DistributesRewardsCorrectly");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create packages
            var platinumPackage = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            dbContext.Packages.Add(platinumPackage);

            // Create package reward percentages for all 10 levels
            for (int i = 1; i <= 10; i++)
            {
                dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(i, 1, i, 10m, 0m));
            }

            // Create a 10-level referral chain
            var userIds = TestDataGenerator.CreateReferralChain(dbContext, 11, 1, 1);

            // Define meaningful names for the user IDs
            var level1ReferrerId = userIds[9]; // Direct referrer (level 1)
            var level2ReferrerId = userIds[8]; // Level 2 referrer
            var level3ReferrerId = userIds[7]; // Level 3 referrer
            var level4ReferrerId = userIds[6]; // Level 4 referrer
            var level5ReferrerId = userIds[5]; // Level 5 referrer
            var level6ReferrerId = userIds[4]; // Level 6 referrer
            var level7ReferrerId = userIds[3]; // Level 7 referrer
            var level8ReferrerId = userIds[2]; // Level 8 referrer
            var level9ReferrerId = userIds[1]; // Level 9 referrer
            var level10ReferrerId = userIds[0]; // Level 10 referrer (top level)
            var depositUserId = userIds[10]; // User making the deposit

            // Create a deposit for the bottom user
            var deposit = TestDataGenerator.CreateDeposit(1, depositUserId, 1000m);
            dbContext.Deposits.Add(deposit);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(deposit.Id);

            // Assert
            Assert.Equal(10, result.RewardedUsersCount);
            Assert.Equal(1000m, result.TotalRzwDistributed); // 10 levels * 10% * 1000 = 1000 (actual RZW amounts)
            Assert.Equal(0m, result.TotalTlDistributed); // 10 levels * 0% * 1000 = 0 (actual TL amounts)

            // Verify rewards were created
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Equal(10, rewards.Count);

            // Verify each level's reward
            for (int i = 0; i < 10; i++)
            {
                var reward = rewards.FirstOrDefault(r => r.Level == i + 1);
                Assert.NotNull(reward);

                // Get the expected referrer ID based on the level
                int expectedReferrerId;
                switch (i + 1)
                {
                    case 1: expectedReferrerId = level1ReferrerId; break;
                    case 2: expectedReferrerId = level2ReferrerId; break;
                    case 3: expectedReferrerId = level3ReferrerId; break;
                    case 4: expectedReferrerId = level4ReferrerId; break;
                    case 5: expectedReferrerId = level5ReferrerId; break;
                    case 6: expectedReferrerId = level6ReferrerId; break;
                    case 7: expectedReferrerId = level7ReferrerId; break;
                    case 8: expectedReferrerId = level8ReferrerId; break;
                    case 9: expectedReferrerId = level9ReferrerId; break;
                    case 10: expectedReferrerId = level10ReferrerId; break;
                    default: expectedReferrerId = 0; break; // Should never happen
                }

                Assert.Equal(expectedReferrerId, reward.UserId);
                Assert.Equal(depositUserId, reward.ReferredUserId);
                Assert.Equal(10m, reward.RzwPercentage);
                Assert.Equal(0m, reward.TlPercentage);
                Assert.Equal(100m, reward.RzwAmount); // 1000 * 0.10 = 100
                Assert.Equal(0m, reward.TlAmount); // 1000 * 0 = 0
            }

            // Verify wallet service was called for each level
            var rzwTokenId = await mockTokenPriceService.Object.GetRzwTokenIdAsync();

            // Verify wallet service calls for each referrer (10 levels)
            var referrerIds = new[] { level1ReferrerId, level2ReferrerId, level3ReferrerId, level4ReferrerId, level5ReferrerId,
                                     level6ReferrerId, level7ReferrerId, level8ReferrerId, level9ReferrerId, level10ReferrerId };

            foreach (var referrerId in referrerIds)
            {
                mockWalletService.Verify(x => x.AddAvailableBalanceAsync(
                    It.Is<int>(userId => userId == referrerId),
                    It.Is<RzwTokenInfo>(info => info.TokenId == rzwTokenId),
                    It.Is<decimal>(amount => amount == 100m),
                    It.IsAny<TradeType>(),
                    It.IsAny<AppDbContext>()), Times.Once);
            }

            // Verify trade records were created for each level - 10 calls to CreateAsync
            mockTradeService.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Exactly(10));

            // Verify trade service calls for each referrer with detailed balance checks
            mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == level1ReferrerId &&
                t.CoinId == rzwTokenId &&
                t.CoinAmount == 100m &&
                t.TryAmount == 100m &&
                t.Type == TradeType.ReferralReward &&
                t.CoinRate == 1.0m && // RZW price
                t.PreviousCoinBalance == 0m && // No previous RZW balance
                t.NewCoinBalance == 100m // New RZW balance after reward
            )), Times.Once);

            // Verify trade service calls for level 2
            mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == level2ReferrerId &&
                t.CoinId == rzwTokenId &&
                t.CoinAmount == 100m &&
                t.TryAmount == 100m &&
                t.Type == TradeType.ReferralReward &&
                t.CoinRate == 1.0m && // RZW price
                t.PreviousCoinBalance == 0m && // No previous RZW balance
                t.NewCoinBalance == 100m // New RZW balance after reward
            )), Times.Once);

            // Verify trade service calls for level 3
            mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == level3ReferrerId &&
                t.CoinId == rzwTokenId &&
                t.CoinAmount == 100m &&
                t.TryAmount == 100m &&
                t.Type == TradeType.ReferralReward &&
                t.CoinRate == 1.0m && // RZW price
                t.PreviousCoinBalance == 0m && // No previous RZW balance
                t.NewCoinBalance == 100m // New RZW balance after reward
            )), Times.Once);

            // Verify trade service calls for level 4
            mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == level4ReferrerId &&
                t.CoinId == rzwTokenId &&
                t.CoinAmount == 100m &&
                t.TryAmount == 100m &&
                t.Type == TradeType.ReferralReward &&
                t.CoinRate == 1.0m && // RZW price
                t.PreviousCoinBalance == 0m && // No previous RZW balance
                t.NewCoinBalance == 100m // New RZW balance after reward
            )), Times.Once);

            // Verify trade service calls for level 5
            mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == level5ReferrerId &&
                t.CoinId == rzwTokenId &&
                t.CoinAmount == 100m &&
                t.TryAmount == 100m &&
                t.Type == TradeType.ReferralReward &&
                t.CoinRate == 1.0m && // RZW price
                t.PreviousCoinBalance == 0m && // No previous RZW balance
                t.NewCoinBalance == 100m // New RZW balance after reward
            )), Times.Once);

            // Verify trade service calls for level 6
            mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == level6ReferrerId &&
                t.CoinId == rzwTokenId &&
                t.CoinAmount == 100m &&
                t.TryAmount == 100m &&
                t.Type == TradeType.ReferralReward &&
                t.CoinRate == 1.0m && // RZW price
                t.PreviousCoinBalance == 0m && // No previous RZW balance
                t.NewCoinBalance == 100m // New RZW balance after reward
            )), Times.Once);

            // Verify trade service calls for level 7
            mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == level7ReferrerId &&
                t.CoinId == rzwTokenId &&
                t.CoinAmount == 100m &&
                t.TryAmount == 100m &&
                t.Type == TradeType.ReferralReward &&
                t.CoinRate == 1.0m && // RZW price
                t.PreviousCoinBalance == 0m && // No previous RZW balance
                t.NewCoinBalance == 100m // New RZW balance after reward
            )), Times.Once);

            // Verify trade service calls for level 8
            mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == level8ReferrerId &&
                t.CoinId == rzwTokenId &&
                t.CoinAmount == 100m &&
                t.TryAmount == 100m &&
                t.Type == TradeType.ReferralReward &&
                t.CoinRate == 1.0m && // RZW price
                t.PreviousCoinBalance == 0m && // No previous RZW balance
                t.NewCoinBalance == 100m // New RZW balance after reward
            )), Times.Once);

            // Verify trade service calls for level 9
            mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == level9ReferrerId &&
                t.CoinId == rzwTokenId &&
                t.CoinAmount == 100m &&
                t.TryAmount == 100m &&
                t.Type == TradeType.ReferralReward &&
                t.CoinRate == 1.0m && // RZW price
                t.PreviousCoinBalance == 0m && // No previous RZW balance
                t.NewCoinBalance == 100m // New RZW balance after reward
            )), Times.Once);

            // Verify trade service calls for level 10
            mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == level10ReferrerId &&
                t.CoinId == rzwTokenId &&
                t.CoinAmount == 100m &&
                t.TryAmount == 100m &&
                t.Type == TradeType.ReferralReward &&
                t.CoinRate == 1.0m && // RZW price
                t.PreviousCoinBalance == 0m && // No previous RZW balance
                t.NewCoinBalance == 100m // New RZW balance after reward
            )), Times.Once);

            // Verify deposit status was updated
            var updatedPayment = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.Distributed, updatedPayment!.RewardStatus);
        }

        /// <summary>
        /// Tests that no rewards are distributed when a user's package has expired.
        /// This test creates a referral chain and a deposit, but the referrer's package
        /// has expired, so no rewards should be distributed.
        ///
        /// The test verifies:
        /// - No rewards are created
        /// - The wallet service is not called
        /// - The trade service is not called
        /// - The deposit status is updated to NoRewards
        /// </summary>
        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithExpiredPackage_ReturnsNoRewards()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithExpiredPackage_ReturnsNoRewards");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create packages
            var platinumPackage = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            dbContext.Packages.Add(platinumPackage);

            // Create package reward percentages
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(1, 1, 1, 20m, 0m));

            // Create users with expired package
            var referrer = TestDataGenerator.CreateUser(1);
            var user = TestDataGenerator.CreateUser(2, "REF2", referrer.UserId);

            dbContext.Users.Add(referrer);
            dbContext.Users.Add(user);

            // Create an expired package for the referrer
            var expiredPackage = TestDataGenerator.CreateUserPackage(1, referrer.UserId, 1, DateTime.UtcNow.AddDays(-1));
            expiredPackage.Status = UserPackageStatus.Expired; // Set status to Expired
            dbContext.UserPackages.Add(expiredPackage);

            // Create a deposit for the user
            var deposit = TestDataGenerator.CreateDeposit(1, user.UserId, 1000m);
            dbContext.Deposits.Add(deposit);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(deposit.Id);

            // Assert
            Assert.Equal(0, result.RewardedUsersCount);
            Assert.Equal(0m, result.TotalRzwDistributed);

            // Verify no rewards were created
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Empty(rewards);

            // Verify wallet service was not called
            mockWalletService.Verify(x => x.AddAvailableBalanceAsync(
                It.IsAny<int>(),
                It.IsAny<RzwTokenInfo>(),
                It.IsAny<decimal>(),
                It.IsAny<TradeType>(),
                It.IsAny<AppDbContext>()), Times.Never);

            // Verify trade service was not called
            mockTradeService.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Never);

            // Verify deposit status was updated
            var updatedPayment = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.NoRewards, updatedPayment!.RewardStatus);
        }

        /// <summary>
        /// Tests that no rewards are distributed when a user's package has zero reward percentage.
        /// This test creates a referral chain and a deposit, but the referrer's package
        /// has a zero reward percentage, so no rewards should be distributed.
        ///
        /// The test verifies:
        /// - No rewards are created
        /// - The wallet service is not called
        /// - The trade service is not called
        /// - The deposit status is updated to NoRewards
        /// </summary>
        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithZeroRewardPercentage_ReturnsNoRewards()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithZeroRewardPercentage_ReturnsNoRewards");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create packages
            var platinumPackage = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            dbContext.Packages.Add(platinumPackage);

            // Create package reward percentages with zero percentage
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(1, 1, 1, 0m, 0m));

            // Create users
            var referrer = TestDataGenerator.CreateUser(1);
            var user = TestDataGenerator.CreateUser(2, "REF2", referrer.UserId);

            dbContext.Users.Add(referrer);
            dbContext.Users.Add(user);

            // Create a package for the referrer
            var userPackage = TestDataGenerator.CreateUserPackage(1, referrer.UserId, 1);
            dbContext.UserPackages.Add(userPackage);

            // Create a deposit for the user
            var deposit = TestDataGenerator.CreateDeposit(1, user.UserId, 1000m);
            dbContext.Deposits.Add(deposit);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(deposit.Id);

            // Assert
            Assert.Equal(0, result.RewardedUsersCount);
            Assert.Equal(0m, result.TotalRzwDistributed);

            // Verify no rewards were created
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Empty(rewards);

            // Verify wallet service was not called
            mockWalletService.Verify(x => x.AddAvailableBalanceAsync(
                It.IsAny<int>(),
                It.IsAny<RzwTokenInfo>(),
                It.IsAny<decimal>(),
                It.IsAny<TradeType>(),
                It.IsAny<AppDbContext>()), Times.Never);

            // Verify trade service was not called
            mockTradeService.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Never);

            // Verify deposit status was updated
            var updatedPayment = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.NoRewards, updatedPayment!.RewardStatus);
        }

        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithZeroAmount_DistributesZeroRewards()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithZeroAmount_DistributesZeroRewards");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create packages
            var platinumPackage = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            dbContext.Packages.Add(platinumPackage);

            // Create package reward percentages
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(1, 1, 1, 20m, 0m)); // 20% RZW, 0% TL for level 1

            // Create a 2-level referral chain
            var userIds = TestDataGenerator.CreateReferralChain(dbContext, 2, 1, 1);
            var referrerUserId = userIds[0]; // The user who will receive the referral reward
            var depositUserId = userIds[1]; // The user making the deposit

            // Create a payment with zero amount
            var payment = TestDataGenerator.CreateDeposit(1, depositUserId, 0m);
            dbContext.Deposits.Add(payment);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(payment.Id);

            // Assert
            Assert.Equal(1, result.RewardedUsersCount); // User should still be counted
            Assert.Equal(0m, result.TotalRzwDistributed); // But reward amount is 0

            // Verify rewards were created with zero amount
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Single(rewards);

            // Level 1 reward (20% of 0 = 0)
            var level1Reward = rewards.First();
            Assert.Equal(referrerUserId, level1Reward.UserId);
            Assert.Equal(depositUserId, level1Reward.ReferredUserId);
            Assert.Equal(20m, level1Reward.RzwPercentage);
            Assert.Equal(0m, level1Reward.TlPercentage);
            Assert.Equal(0m, level1Reward.RzwAmount); // 0 * 0.20 = 0
            Assert.Equal(0m, level1Reward.TlAmount); // 0 * 0 = 0

            // Verify wallet service was NOT called (RZW amount is 0, so no wallet operation should occur)
            mockWalletService.Verify(x => x.AddAvailableBalanceAsync(
                It.IsAny<int>(),
                It.IsAny<RzwTokenInfo>(),
                It.IsAny<decimal>(),
                It.IsAny<TradeType>(),
                It.IsAny<AppDbContext>()), Times.Never);

            // Verify trade service was NOT called (RZW amount is 0, so no trade record should be created)
            mockTradeService.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Never);

            // Verify no balance transactions were created (TL amount is 0)
            var balanceTransactions = await dbContext.BalanceTransactions.ToListAsync();
            Assert.Empty(balanceTransactions);

            // Verify user's TL balance was NOT updated (TL reward is 0)
            var referrerUser = await dbContext.Users.FindAsync(referrerUserId);
            Assert.NotNull(referrerUser);
            Assert.Equal(0m, referrerUser!.Balance); // Should remain 0 since no TL reward was added

            // Verify payment status was updated
            var updatedPayment = await dbContext.Deposits.FindAsync(payment.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.Distributed, updatedPayment!.RewardStatus);
        }

        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithCircularReferrals_HandlesCorrectly()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithCircularReferrals_HandlesCorrectly");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create packages
            var platinumPackage = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            dbContext.Packages.Add(platinumPackage);

            // Create package reward percentages
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(1, 1, 1, 20m, 0m)); // 20% RZW, 0% TL for level 1
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(2, 1, 2, 15m, 0m)); // 15% RZW, 0% TL for level 2
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(3, 1, 3, 10m, 0m)); // 10% RZW, 0% TL for level 3

            // Create users with a circular referral chain
            var circularUser1 = TestDataGenerator.CreateUser(1, "REF1", 3); // User 1 is referred by User 3
            var circularUser2 = TestDataGenerator.CreateUser(2, "REF2", 1); // User 2 is referred by User 1
            var circularUser3 = TestDataGenerator.CreateUser(3, "REF3", 2); // User 3 is referred by User 2
            var depositUser = TestDataGenerator.CreateUser(4, "REF4", 3); // User 4 is referred by User 3 (will make the deposit)

            dbContext.Users.AddRange(circularUser1, circularUser2, circularUser3, depositUser);

            // Assign packages to users
            dbContext.UserPackages.Add(TestDataGenerator.CreateUserPackage(1, circularUser1.UserId, 1));
            dbContext.UserPackages.Add(TestDataGenerator.CreateUserPackage(2, circularUser2.UserId, 1));
            dbContext.UserPackages.Add(TestDataGenerator.CreateUserPackage(3, circularUser3.UserId, 1));

            // Create a payment for deposit user
            var payment = TestDataGenerator.CreateDeposit(1, depositUser.UserId, 1000m);
            dbContext.Deposits.Add(payment);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(payment.Id);

            // Assert
            Assert.Equal(3, result.RewardedUsersCount); // All 3 users in the circular chain should get rewards
            Assert.Equal(450m, result.TotalRzwDistributed); // 200 + 150 + 100 = 450 (actual RZW amounts)
            Assert.Equal(0m, result.TotalTlDistributed); // 0 + 0 + 0 = 0 (actual TL amounts)

            // Verify rewards were created
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Equal(3, rewards.Count);

            // Level 1 reward (20%)
            var level1Reward = rewards.FirstOrDefault(r => r.Level == 1);
            Assert.NotNull(level1Reward);
            Assert.Equal(circularUser3.UserId, level1Reward.UserId);
            Assert.Equal(depositUser.UserId, level1Reward.ReferredUserId);
            Assert.Equal(20m, level1Reward.RzwPercentage);
            Assert.Equal(0m, level1Reward.TlPercentage);
            Assert.Equal(200m, level1Reward.RzwAmount); // 1000 * 0.20 = 200
            Assert.Equal(0m, level1Reward.TlAmount); // 1000 * 0 = 0

            // Level 2 reward (15%)
            var level2Reward = rewards.FirstOrDefault(r => r.Level == 2);
            Assert.NotNull(level2Reward);
            Assert.Equal(circularUser2.UserId, level2Reward.UserId);
            Assert.Equal(depositUser.UserId, level2Reward.ReferredUserId);
            Assert.Equal(15m, level2Reward.RzwPercentage);
            Assert.Equal(0m, level2Reward.TlPercentage);
            Assert.Equal(150m, level2Reward.RzwAmount); // 1000 * 0.15 = 150
            Assert.Equal(0m, level2Reward.TlAmount); // 1000 * 0 = 0

            // Level 3 reward (10%)
            var level3Reward = rewards.FirstOrDefault(r => r.Level == 3);
            Assert.NotNull(level3Reward);
            Assert.Equal(circularUser1.UserId, level3Reward.UserId);
            Assert.Equal(depositUser.UserId, level3Reward.ReferredUserId);
            Assert.Equal(10m, level3Reward.RzwPercentage);
            Assert.Equal(0m, level3Reward.TlPercentage);
            Assert.Equal(100m, level3Reward.RzwAmount); // 1000 * 0.10 = 100
            Assert.Equal(0m, level3Reward.TlAmount); // 1000 * 0 = 0

            // Verify wallet service was called
            var rzwTokenId = await mockTokenPriceService.Object.GetRzwTokenIdAsync();
            mockWalletService.Verify(x => x.AddAvailableBalanceAsync(
                It.Is<int>(userId => userId == circularUser3.UserId),
                It.Is<RzwTokenInfo>(info => info.TokenId == rzwTokenId),
                It.Is<decimal>(amount => amount == 200m),
                It.IsAny<TradeType>(),
                It.IsAny<AppDbContext>()), Times.Once);
            mockWalletService.Verify(x => x.AddAvailableBalanceAsync(
                It.Is<int>(userId => userId == circularUser2.UserId),
                It.Is<RzwTokenInfo>(info => info.TokenId == rzwTokenId),
                It.Is<decimal>(amount => amount == 150m),
                It.IsAny<TradeType>(),
                It.IsAny<AppDbContext>()), Times.Once);
            mockWalletService.Verify(x => x.AddAvailableBalanceAsync(
                It.Is<int>(userId => userId == circularUser1.UserId),
                It.Is<RzwTokenInfo>(info => info.TokenId == rzwTokenId),
                It.Is<decimal>(amount => amount == 100m),
                It.IsAny<TradeType>(),
                It.IsAny<AppDbContext>()), Times.Once);

            // Verify payment status was updated
            var updatedPayment = await dbContext.Deposits.FindAsync(payment.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.Distributed, updatedPayment!.RewardStatus);
        }

        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithWalletServiceFailure_HandlesErrorCorrectly()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithWalletServiceFailure_HandlesErrorCorrectly");
            var mockWalletService = CreateMockWalletServiceWithFailure();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create packages
            var platinumPackage = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            dbContext.Packages.Add(platinumPackage);

            // Create package reward percentages
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(1, 1, 1, 20m, 0m));

            // Create users
            var referrerUser = TestDataGenerator.CreateUser(1);
            var depositUser = TestDataGenerator.CreateUser(2, "REF2", referrerUser.UserId);

            dbContext.Users.Add(referrerUser);
            dbContext.Users.Add(depositUser);

            // Create a package for the referrer
            var userPackage = TestDataGenerator.CreateUserPackage(1, referrerUser.UserId, 1);
            dbContext.UserPackages.Add(userPackage);

            // Create a deposit
            var deposit = TestDataGenerator.CreateDeposit(1, depositUser.UserId, 1000m);
            dbContext.Deposits.Add(deposit);
            dbContext.SaveChanges();

            // Create the service (wallet service is already configured to fail)
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => service.ProcessDepositRewardsAsync(deposit.Id));

            // Verify rewards were created but not processed successfully
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Single(rewards); // One reward should be created before the wallet service fails

            // Verify trade service was not called
            mockTradeService.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Never);

            // Verify deposit status was updated to Failed
            var updatedPayment = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.Failed, updatedPayment!.RewardStatus);
        }

        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithVeryLargeAmount_HandlesDecimalPrecisionCorrectly()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithVeryLargeAmount_HandlesDecimalPrecisionCorrectly");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create packages
            var platinumPackage = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            dbContext.Packages.Add(platinumPackage);

            // Create package reward percentages
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(1, 1, 1, 5m, 15m)); // 5% RZW, 15% TL for level 1 (25% RZW, 75% TL split)

            // Create users
            var referrerUser = TestDataGenerator.CreateUser(1);
            var depositUser = TestDataGenerator.CreateUser(2, "REF2", referrerUser.UserId);

            dbContext.Users.Add(referrerUser);
            dbContext.Users.Add(depositUser);

            // Create a package for the referrer
            var userPackage = TestDataGenerator.CreateUserPackage(1, referrerUser.UserId, 1);
            dbContext.UserPackages.Add(userPackage);

            // Create a deposit with a very large amount
            var deposit = TestDataGenerator.CreateDeposit(1, depositUser.UserId, 1000000000m);
            dbContext.Deposits.Add(deposit);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(deposit.Id);

            // Assert
            Assert.Equal(1, result.RewardedUsersCount);
            Assert.Equal(50000000m, result.TotalRzwDistributed); // 5% of 1,000,000,000

            // Verify rewards were created
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Single(rewards);

            var reward = rewards[0];
            Assert.Equal(referrerUser.UserId, reward.UserId);
            Assert.Equal(depositUser.UserId, reward.ReferredUserId);
            Assert.Equal(5m, reward.RzwPercentage);
            Assert.Equal(15m, reward.TlPercentage);
            Assert.Equal(50000000m, reward.RzwAmount); // 1,000,000,000 * 0.05 = 50,000,000
            Assert.Equal(150000000m, reward.TlAmount); // 1,000,000,000 * 0.15 = 150,000,000

            // Verify wallet service was called
            var rzwTokenId = await mockTokenPriceService.Object.GetRzwTokenIdAsync();
            mockWalletService.Verify(x => x.AddAvailableBalanceAsync(
                It.Is<int>(userId => userId == referrerUser.UserId),
                It.Is<RzwTokenInfo>(info => info.TokenId == rzwTokenId),
                It.Is<decimal>(amount => amount == 50000000m),
                It.IsAny<TradeType>(),
                It.IsAny<AppDbContext>()), Times.Once);

            // Verify trade service was called once with detailed balance checks
            mockTradeService.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Once);
            mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == referrerUser.UserId &&
                t.CoinId == rzwTokenId &&
                t.CoinAmount == 50000000m &&
                t.TryAmount == 50000000m &&
                t.Type == TradeType.ReferralReward &&
                t.CoinRate == 1.0m && // RZW price
                t.PreviousCoinBalance == 0m && // No previous RZW balance
                t.NewCoinBalance == 50000000m // New RZW balance after reward
            )), Times.Once);

            // Verify deposit status was updated
            var updatedPayment = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.Distributed, updatedPayment!.RewardStatus);
        }

        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithVerySmallAmount_HandlesDecimalPrecisionCorrectly()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithVerySmallAmount_HandlesDecimalPrecisionCorrectly");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create packages
            var platinumPackage = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            dbContext.Packages.Add(platinumPackage);

            // Create package reward percentages
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(1, 1, 1, 5m, 15m)); // 5% RZW, 15% TL for level 1

            // Create a 2-level referral chain
            var userIds = TestDataGenerator.CreateReferralChain(dbContext, 2, 1, 1);
            var referrerUserId = userIds[0]; // The user who will receive the referral reward
            var depositUserId = userIds[1]; // The user making the deposit

            // Create a payment with a very small amount
            var payment = TestDataGenerator.CreateDeposit(1, depositUserId, 0.00000001m); // Very small amount
            dbContext.Deposits.Add(payment);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(payment.Id);

            // Assert
            Assert.Equal(1, result.RewardedUsersCount);
            Assert.Equal(0.0000000005m, result.TotalRzwDistributed); // 0.00000001 * 0.05 = 0.0000000005 (actual RZW amount)
            Assert.Equal(0.0000000015m, result.TotalTlDistributed); // 0.00000001 * 0.15 = 0.0000000015 (actual TL amount)

            // Verify rewards were created
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Single(rewards);

            // Level 1 reward (20%)
            var level1Reward = rewards.First();
            Assert.Equal(referrerUserId, level1Reward.UserId);
            Assert.Equal(depositUserId, level1Reward.ReferredUserId);
            Assert.Equal(5m, level1Reward.RzwPercentage); // 25% of 20% = 5%
            Assert.Equal(15m, level1Reward.TlPercentage); // 75% of 20% = 15%
            Assert.Equal(0.0000000005m, level1Reward.RzwAmount); // 0.00000001 * 0.05 = 0.0000000005
            Assert.Equal(0.0000000015m, level1Reward.TlAmount); // 0.00000001 * 0.15 = 0.0000000015

            // Verify wallet service was called
            var rzwTokenId = await mockTokenPriceService.Object.GetRzwTokenIdAsync();
            mockWalletService.Verify(x => x.AddAvailableBalanceAsync(
                It.Is<int>(userId => userId == referrerUserId),
                It.Is<RzwTokenInfo>(info => info.TokenId == rzwTokenId),
                It.Is<decimal>(amount => amount == 0.0000000005m),
                It.IsAny<TradeType>(),
                It.IsAny<AppDbContext>()), Times.Once);

            // Verify payment status was updated
            var updatedPayment = await dbContext.Deposits.FindAsync(payment.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.Distributed, updatedPayment!.RewardStatus);
        }
    }
}
