using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Tests.TestInfrastructure.Base;
using RazeWinComTr.Tests.TestInfrastructure.Factories;
using RazeWinComTr.Tests.TestInfrastructure.Utilities;
using Xunit;

namespace RazeWinComTr.Tests.Unit.Services
{
    /// <summary>
    /// Unit tests for WalletService using simple .NET object initialization.
    /// Demonstrates the clean, pattern-free approach to test data creation.
    /// </summary>
    public class WalletServiceTests : TestBase
    {
        [Fact]
        public async Task GetByUserIdAndCoinIdAsync_WithExistingWallet_ReturnsWallet()
        {
            // Arrange - Simple object creation, no complex patterns
            var dbName = CreateUniqueDatabaseName(nameof(GetByUserIdAndCoinIdAsync_WithExistingWallet_ReturnsWallet));
            using var context = CreateDbContext(dbName);

            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "1234567890",
                IdentityNumber = "12345678901",
                ReferralCode = "REF001",
                IsActive = 1,
                CrDate = DateTime.UtcNow,
                BirthDate = DateTime.UtcNow.AddYears(-25),
                PasswordHash = "test-hash"
            };

            var wallet = new Wallet 
            { 
                UserId = 1, 
                CoinId = 1, 
                Balance = 100.50m,
                CreatedDate = DateTime.UtcNow
            };

            context.Users.Add(user);
            context.Wallets.Add(wallet);
            await context.SaveChangesAsync();

            var service = new WalletService(context);

            // Act
            var result = await service.GetByUserIdAndCoinIdAsync(1, 1);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.UserId);
            Assert.Equal(1, result.CoinId);
            Assert.Equal(100.50m, result.Balance);
        }

        [Fact]
        public async Task GetByUserIdAndCoinIdAsync_WithNonExistentWallet_ReturnsNull()
        {
            // Arrange - Even simpler when we don't need data
            var dbName = CreateUniqueDatabaseName(nameof(GetByUserIdAndCoinIdAsync_WithNonExistentWallet_ReturnsNull));
            using var context = CreateDbContext(dbName);

            var service = new WalletService(context);

            // Act
            var result = await service.GetByUserIdAndCoinIdAsync(999, 999);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task CreateAsync_WithValidWallet_CreatesWallet()
        {
            // Arrange
            var dbName = CreateUniqueDatabaseName(nameof(CreateAsync_WithValidWallet_CreatesWallet));
            using var context = CreateDbContext(dbName);

            // Create user first (required for foreign key)
            var user = CreateUser(1); // Using our simple helper
            context.Users.Add(user);
            await context.SaveChangesAsync();

            var service = new WalletService(context);

            var newWallet = new Wallet 
            { 
                UserId = 1, 
                CoinId = 2, 
                Balance = 50.25m 
            };

            // Act
            var result = await service.CreateAsync(newWallet);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Id > 0); // Should have been assigned an ID
            Assert.Equal(1, result.UserId);
            Assert.Equal(2, result.CoinId);
            Assert.Equal(50.25m, result.Balance);

            // Verify it was saved to database
            var savedWallet = await context.Wallets.FindAsync(result.Id);
            Assert.NotNull(savedWallet);
            Assert.Equal(50.25m, savedWallet.Balance);
        }

        [Fact]
        public async Task GetByUserIdAsync_WithMultipleWallets_ReturnsAllUserWallets()
        {
            // Arrange
            var dbName = CreateUniqueDatabaseName(nameof(GetByUserIdAsync_WithMultipleWallets_ReturnsAllUserWallets));
            using var context = CreateDbContext(dbName);

            // Create user and multiple wallets - simple and clear
            var user = CreateUser(1);
            var wallet1 = new Wallet { UserId = 1, CoinId = 1, Balance = 100m, CreatedDate = DateTime.UtcNow };
            var wallet2 = new Wallet { UserId = 1, CoinId = 2, Balance = 200m, CreatedDate = DateTime.UtcNow };
            var wallet3 = new Wallet { UserId = 2, CoinId = 1, Balance = 300m, CreatedDate = DateTime.UtcNow }; // Different user

            context.Users.Add(user);
            context.Users.Add(CreateUser(2)); // Second user
            context.Wallets.AddRange(wallet1, wallet2, wallet3);
            await context.SaveChangesAsync();

            var service = new WalletService(context);

            // Act
            var result = await service.GetByUserIdAsync(1);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Should only return wallets for user 1
            Assert.All(result, w => Assert.Equal(1, w.UserId));
            Assert.Contains(result, w => w.CoinId == 1 && w.Balance == 100m);
            Assert.Contains(result, w => w.CoinId == 2 && w.Balance == 200m);
        }

        [Fact]
        public async Task GetByUserIdAsync_WithNoWallets_ReturnsEmptyList()
        {
            // Arrange
            var dbName = CreateUniqueDatabaseName(nameof(GetByUserIdAsync_WithNoWallets_ReturnsEmptyList));
            using var context = CreateDbContext(dbName);

            var logger = MockServiceFactory.CreateLogger<IWalletService>();
            var localizer = MockServiceFactory.CreateLocalizer<SharedResource>();
            var service = new WalletService(context, logger.Object, localizer.Object);

            // Act
            var result = await service.GetByUserIdAsync(999);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }
    }
}
