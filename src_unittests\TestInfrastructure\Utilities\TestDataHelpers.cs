using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Tests.TestInfrastructure.Utilities
{
    /// <summary>
    /// Simple helper methods for creating test data using natural .NET object initialization.
    /// No complex patterns - just straightforward object creation.
    /// </summary>
    public static class TestDataHelpers
    {
        // ==================== USER HELPERS ====================

        /// <summary>
        /// Creates a simple test user with basic properties.
        /// </summary>
        public static User CreateUser(int userId = 1, string? email = null, string? referralCode = null, int? referrerId = null)
        {
            return new User
            {
                UserId = userId,
                Email = email ?? $"user{userId}@test.com",
                FirstName = $"User{userId}",
                LastName = "Test",
                Phone = "1234567890",
                ReferralCode = referralCode ?? $"REF{userId:D3}",
                ReferrerId = referrerId,
                IsActive = 1,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a list of users forming a referral chain.
        /// User 1 has no referrer, User 2 referred by User 1, etc.
        /// </summary>
        public static List<User> CreateReferralChain(int count, int startUserId = 1)
        {
            var users = new List<User>();
            
            for (int i = 0; i < count; i++)
            {
                var userId = startUserId + i;
                var referrerId = i > 0 ? startUserId + i - 1 : (int?)null;
                
                users.Add(CreateUser(userId, referrerId: referrerId));
            }
            
            return users;
        }

        // ==================== PACKAGE HELPERS ====================

        /// <summary>
        /// Creates a Bronze package (1 level, 5% reward).
        /// </summary>
        public static Package CreateBronzePackage()
        {
            return new Package
            {
                Id = 1,
                Name = "Bronze",
                Price = 100m,
                Description = "Bronze package with 1 level referral",
                IsActive = 1,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a Silver package (2 levels, 10% and 5% rewards).
        /// </summary>
        public static Package CreateSilverPackage()
        {
            return new Package
            {
                Id = 2,
                Name = "Silver",
                Price = 250m,
                Description = "Silver package with 2 level referral",
                IsActive = 1,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a Gold package (3 levels, 15%, 10%, and 5% rewards).
        /// </summary>
        public static Package CreateGoldPackage()
        {
            return new Package
            {
                Id = 3,
                Name = "Gold",
                Price = 500m,
                Description = "Gold package with 3 level referral",
                IsActive = 1,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a Platinum package (4 levels, 20%, 15%, 10%, and 5% rewards).
        /// </summary>
        public static Package CreatePlatinumPackage()
        {
            return new Package
            {
                Id = 4,
                Name = "Platinum",
                Price = 1000m,
                Description = "Platinum package with 4 level referral",
                IsActive = 1,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates all standard packages.
        /// </summary>
        public static List<Package> CreateAllStandardPackages()
        {
            return new List<Package>
            {
                CreateBronzePackage(),
                CreateSilverPackage(),
                CreateGoldPackage(),
                CreatePlatinumPackage()
            };
        }

        // ==================== PACKAGE REWARD PERCENTAGE HELPERS ====================

        /// <summary>
        /// Creates reward percentages for Bronze package.
        /// </summary>
        public static List<PackageRewardPercentage> CreateBronzeRewardPercentages()
        {
            return new List<PackageRewardPercentage>
            {
                new PackageRewardPercentage { PackageId = 1, Level = 1, Percentage = 5m, IsActive = 1, CreatedDate = DateTime.UtcNow, ModifiedDate = DateTime.UtcNow }
            };
        }

        /// <summary>
        /// Creates reward percentages for Silver package.
        /// </summary>
        public static List<PackageRewardPercentage> CreateSilverRewardPercentages()
        {
            return new List<PackageRewardPercentage>
            {
                new PackageRewardPercentage { PackageId = 2, Level = 1, Percentage = 10m, IsActive = 1, CreatedDate = DateTime.UtcNow, ModifiedDate = DateTime.UtcNow },
                new PackageRewardPercentage { PackageId = 2, Level = 2, Percentage = 5m, IsActive = 1, CreatedDate = DateTime.UtcNow, ModifiedDate = DateTime.UtcNow }
            };
        }

        /// <summary>
        /// Creates reward percentages for Gold package.
        /// </summary>
        public static List<PackageRewardPercentage> CreateGoldRewardPercentages()
        {
            return new List<PackageRewardPercentage>
            {
                new PackageRewardPercentage { PackageId = 3, Level = 1, Percentage = 15m, IsActive = 1, CreatedDate = DateTime.UtcNow, ModifiedDate = DateTime.UtcNow },
                new PackageRewardPercentage { PackageId = 3, Level = 2, Percentage = 10m, IsActive = 1, CreatedDate = DateTime.UtcNow, ModifiedDate = DateTime.UtcNow },
                new PackageRewardPercentage { PackageId = 3, Level = 3, Percentage = 5m, IsActive = 1, CreatedDate = DateTime.UtcNow, ModifiedDate = DateTime.UtcNow }
            };
        }

        /// <summary>
        /// Creates reward percentages for Platinum package.
        /// </summary>
        public static List<PackageRewardPercentage> CreatePlatinumRewardPercentages()
        {
            return new List<PackageRewardPercentage>
            {
                new PackageRewardPercentage { PackageId = 4, Level = 1, Percentage = 20m, IsActive = 1, CreatedDate = DateTime.UtcNow, ModifiedDate = DateTime.UtcNow },
                new PackageRewardPercentage { PackageId = 4, Level = 2, Percentage = 15m, IsActive = 1, CreatedDate = DateTime.UtcNow, ModifiedDate = DateTime.UtcNow },
                new PackageRewardPercentage { PackageId = 4, Level = 3, Percentage = 10m, IsActive = 1, CreatedDate = DateTime.UtcNow, ModifiedDate = DateTime.UtcNow },
                new PackageRewardPercentage { PackageId = 4, Level = 4, Percentage = 5m, IsActive = 1, CreatedDate = DateTime.UtcNow, ModifiedDate = DateTime.UtcNow }
            };
        }

        /// <summary>
        /// Creates all standard reward percentages for all packages.
        /// </summary>
        public static List<PackageRewardPercentage> CreateAllStandardRewardPercentages()
        {
            var percentages = new List<PackageRewardPercentage>();
            percentages.AddRange(CreateBronzeRewardPercentages());
            percentages.AddRange(CreateSilverRewardPercentages());
            percentages.AddRange(CreateGoldRewardPercentages());
            percentages.AddRange(CreatePlatinumRewardPercentages());
            return percentages;
        }

        // ==================== WALLET HELPERS ====================

        /// <summary>
        /// Creates a simple wallet for testing.
        /// </summary>
        public static Wallet CreateWallet(int userId, int coinId, decimal balance = 0m)
        {
            return new Wallet
            {
                UserId = userId,
                CoinId = coinId,
                Balance = balance,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };
        }

        // ==================== DEPOSIT HELPERS ====================

        /// <summary>
        /// Creates a simple deposit for testing.
        /// </summary>
        public static Deposit CreateDeposit(int userId, decimal amount, string status = "Completed")
        {
            return new Deposit
            {
                UserId = userId,
                Amount = amount,
                ProcessStatus = status,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };
        }
    }
}
