using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Tests.TestInfrastructure.Builders
{
    /// <summary>
    /// Builder pattern for creating User entities in tests.
    /// Provides a fluent API for constructing users with various configurations.
    /// 
    /// Usage Example:
    /// var user = new UserBuilder()
    ///     .WithId(1)
    ///     .WithEmail("<EMAIL>")
    ///     .WithReferralCode("REF123")
    ///     .WithReferrer(2)
    ///     .Build();
    /// </summary>
    public class UserBuilder
    {
        private User _user;

        public UserBuilder()
        {
            _user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User",
                Phone = "1234567890",
                ReferralCode = "REF001",
                IsActive = 1,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Sets the user ID.
        /// </summary>
        public UserBuilder WithId(int userId)
        {
            _user.UserId = userId;
            return this;
        }

        /// <summary>
        /// Sets the user's email address.
        /// </summary>
        public UserBuilder WithEmail(string email)
        {
            _user.Email = email;
            return this;
        }

        /// <summary>
        /// Sets the user's first name.
        /// </summary>
        public UserBuilder WithFirstName(string firstName)
        {
            _user.FirstName = firstName;
            return this;
        }

        /// <summary>
        /// Sets the user's last name.
        /// </summary>
        public UserBuilder WithLastName(string lastName)
        {
            _user.LastName = lastName;
            return this;
        }

        /// <summary>
        /// Sets the user's phone number.
        /// </summary>
        public UserBuilder WithPhone(string phone)
        {
            _user.Phone = phone;
            return this;
        }

        /// <summary>
        /// Sets the user's referral code.
        /// </summary>
        public UserBuilder WithReferralCode(string referralCode)
        {
            _user.ReferralCode = referralCode;
            return this;
        }

        /// <summary>
        /// Sets the user's referrer ID.
        /// </summary>
        public UserBuilder WithReferrer(int? referrerId)
        {
            _user.ReferrerId = referrerId;
            return this;
        }

        /// <summary>
        /// Sets the user's active status.
        /// </summary>
        public UserBuilder WithActiveStatus(int isActive)
        {
            _user.IsActive = isActive;
            return this;
        }

        /// <summary>
        /// Sets the user as active.
        /// </summary>
        public UserBuilder AsActive()
        {
            _user.IsActive = 1;
            return this;
        }

        /// <summary>
        /// Sets the user as inactive.
        /// </summary>
        public UserBuilder AsInactive()
        {
            _user.IsActive = 0;
            return this;
        }

        /// <summary>
        /// Sets the user's creation date.
        /// </summary>
        public UserBuilder WithCreatedDate(DateTime createdDate)
        {
            _user.CreatedDate = createdDate;
            return this;
        }

        /// <summary>
        /// Sets the user's modification date.
        /// </summary>
        public UserBuilder WithModifiedDate(DateTime modifiedDate)
        {
            _user.ModifiedDate = modifiedDate;
            return this;
        }

        /// <summary>
        /// Creates a user with a referral chain.
        /// Automatically generates referral codes based on user ID.
        /// </summary>
        public UserBuilder InReferralChain(int level, int baseUserId = 1)
        {
            var userId = baseUserId + level - 1;
            var referrerId = level > 1 ? baseUserId + level - 2 : (int?)null;
            
            _user.UserId = userId;
            _user.ReferrerId = referrerId;
            _user.ReferralCode = $"REF{userId:D3}";
            _user.Email = $"user{userId}@example.com";
            _user.FirstName = $"User{userId}";
            _user.LastName = "Test";
            
            return this;
        }

        /// <summary>
        /// Builds and returns the configured User entity.
        /// </summary>
        public User Build()
        {
            return _user;
        }

        /// <summary>
        /// Builds a list of users for testing referral chains.
        /// </summary>
        /// <param name="count">Number of users to create</param>
        /// <param name="baseUserId">Starting user ID</param>
        /// <returns>List of users in referral chain</returns>
        public static List<User> BuildReferralChain(int count, int baseUserId = 1)
        {
            var users = new List<User>();
            
            for (int i = 1; i <= count; i++)
            {
                var user = new UserBuilder()
                    .InReferralChain(i, baseUserId)
                    .Build();
                users.Add(user);
            }
            
            return users;
        }

        /// <summary>
        /// Creates a simple user with minimal configuration for quick testing.
        /// </summary>
        public static User Simple(int userId = 1, string? referralCode = null)
        {
            return new UserBuilder()
                .WithId(userId)
                .WithReferralCode(referralCode ?? $"REF{userId:D3}")
                .Build();
        }

        /// <summary>
        /// Creates a user with a specific referrer for testing referral relationships.
        /// </summary>
        public static User WithReferrer(int userId, int referrerId, string? referralCode = null)
        {
            return new UserBuilder()
                .WithId(userId)
                .WithReferrer(referrerId)
                .WithReferralCode(referralCode ?? $"REF{userId:D3}")
                .Build();
        }
    }
}
