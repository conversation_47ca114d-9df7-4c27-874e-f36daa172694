using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;

namespace RazeWinComTr.Tests
{
    /// <summary>
    /// Utility class for generating test data for unit tests.
    /// Provides methods to create various entities like users, packages, and payments with predefined values.
    /// </summary>
    public static class TestDataGenerator
    {
        /// <summary>
        /// Creates a test user with the specified ID and optional referral information.
        /// </summary>
        /// <param name="userId">The ID to assign to the user</param>
        /// <param name="referralCode">Optional referral code (defaults to "REF{userId}")</param>
        /// <param name="referrerId">Optional ID of the user who referred this user</param>
        /// <returns>A new User entity with test data</returns>
        public static User CreateUser(int userId, string? referralCode = null, int? referrerId = null)
        {
            return new User
            {
                UserId = userId,
                Name = $"User{userId}",
                Surname = $"Test{userId}",
                Email = $"user{userId}@test.com",
                PasswordHash = "hash",
                IsActive = 1,
                ReferralCode = referralCode ?? $"REF{userId}",
                ReferrerId = referrerId,
                CrDate = DateTime.UtcNow,
                IdentityNumber = $"1234567890{userId}",
                PhoneNumber = $"555-555-{userId:D4}"
            };
        }

        /// <summary>
        /// Creates a test package with the specified ID, name, and price.
        /// </summary>
        /// <param name="packageId">The ID to assign to the package</param>
        /// <param name="name">The name of the package (e.g., "Bronze", "Silver")</param>
        /// <param name="price">The price of the package in TL</param>
        /// <returns>A new Package entity with test data</returns>
        public static Package CreatePackage(int packageId, string name, decimal price)
        {
            return new Package
            {
                Id = packageId,
                Name = name,
                Price = price,
                IsActive = true,
                Order = packageId,
                CreatedDate = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a test user package association with the specified ID, user ID, and package ID.
        /// </summary>
        /// <param name="id">The ID to assign to the user package</param>
        /// <param name="userId">The ID of the user who owns the package</param>
        /// <param name="packageId">The ID of the package</param>
        /// <param name="purchaseDate">The purchase date of the package (optional)</param>
        /// <returns>A new UserPackage entity with test data</returns>
        public static UserPackage CreateUserPackage(int id, int userId, int packageId, DateTime? purchaseDate = null)
        {
            return new UserPackage
            {
                Id = id,
                UserId = userId,
                PackageId = packageId,
                PurchaseDate = purchaseDate ?? DateTime.UtcNow,
                Status = UserPackageStatus.Active,
                CreatedDate = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a test package reward percentage with the specified ID, package ID, level, RZW percentage and TL percentage.
        /// </summary>
        /// <param name="id">The ID to assign to the package reward percentage</param>
        /// <param name="packageId">The ID of the package</param>
        /// <param name="level">The referral level (1 = direct referral, 2 = second level, etc.)</param>
        /// <param name="rzwPercentage">The RZW reward percentage for this level</param>
        /// <param name="tlPercentage">The TL reward percentage for this level</param>
        /// <returns>A new PackageRewardPercentage entity with test data</returns>
        public static PackageRewardPercentage CreatePackageRewardPercentage(int id, int packageId, int level, decimal rzwPercentage, decimal tlPercentage)
        {
            return new PackageRewardPercentage
            {
                Id = id,
                PackageId = packageId,
                Level = level,
                RzwPercentage = rzwPercentage,
                TlPercentage = tlPercentage,
                CreatedDate = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a test deposit with the specified ID, user ID, amount, and optional status values.
        /// </summary>
        /// <param name="id">The ID to assign to the deposit</param>
        /// <param name="userId">The ID of the user who made the deposit</param>
        /// <param name="amount">The deposit amount in TL</param>
        /// <param name="status">The deposit status (default: Approved)</param>
        /// <param name="rewardStatus">The deposit reward status (default: Pending)</param>
        /// <returns>A new deposit entity with test data</returns>
        public static Deposit CreateDeposit(int id, int userId, decimal amount, DepositStatus status = DepositStatus.Approved, DepositRewardStatus rewardStatus = DepositRewardStatus.Pending)
        {
            return new Deposit
            {
                Id = id,
                UserId = userId,
                Amount = amount,
                Status = status,
                RewardStatus = rewardStatus,
                CreatedDate = DateTime.UtcNow,
                FullName = $"User{userId} Test{userId}",
                IpAddress = "127.0.0.1",
                DepositType = "DEPOSIT"
            };
        }

        /// <summary>
        /// Creates a test referral reward with the specified parameters.
        /// </summary>
        /// <param name="id">The ID to assign to the referral reward</param>
        /// <param name="userId">The ID of the user who received the reward</param>
        /// <param name="referredUserId">The ID of the user whose action triggered the reward</param>
        /// <param name="packageId">The ID of the package that determined the reward percentage</param>
        /// <param name="level">The referral level (1 = direct referral, 2 = second level, etc.)</param>
        /// <param name="amount">The reward amount in RZW tokens</param>
        /// <param name="percentage">The reward percentage that was applied</param>
        /// <param name="originalAmount">The original amount in TL that the percentage was applied to</param>
        /// <returns>A new ReferralReward entity with test data</returns>
        public static ReferralReward CreateReferralReward(int depositId, int id, int userId, int referredUserId, int packageId, int level, decimal amount)
        {
            return new ReferralReward
            {
                DepositId = depositId,
                Id = id,
                UserId = userId,
                ReferredUserId = referredUserId,
                PackageId = packageId,
                Level = level,
                RzwAmount = amount,
                TlAmount = 0, // No TL amount
                RzwPercentage = 20m, // Default RZW percentage (was 5m + 15m = 20m total)
                TlPercentage = 0, // No TL percentage
                DepositAmount = amount, // Original amount equals RZW amount
                Status = ReferralRewardStatus.Paid,
                DepositDate = DateTime.UtcNow,
                RzwPrice = 1.0m,
                RewardType = "DEPOSIT"
            };
        }

        public static ReferralReward CreateReferralReward(int id, int userId, int referredUserId, int packageId, int level, decimal amount, decimal percentage, decimal originalAmount)
        {
            return new ReferralReward
            {
                Id = id,
                UserId = userId,
                ReferredUserId = referredUserId,
                PackageId = packageId,
                Level = level,
                RzwAmount = amount,
                TlAmount = 0, // No TL amount
                RzwPercentage = percentage, // 100% of total percentage goes to RZW
                TlPercentage = 0, // 0% of total percentage goes to TL
                DepositAmount = originalAmount,
                Status = ReferralRewardStatus.Paid,
                DepositDate = DateTime.UtcNow,
                RzwPrice = 1.0m,
                RewardType = "DEPOSIT"
            };
        }

        /// <summary>
        /// Creates a referral chain with the specified number of levels
        /// </summary>
        /// <param name="context">The database context</param>
        /// <param name="levels">Number of levels in the referral chain</param>
        /// <param name="startUserId">The ID of the first user in the chain</param>
        /// <param name="packageId">The package ID to assign to all users</param>
        /// <returns>A list of user IDs in the chain, starting from the top</returns>
        public static List<int> CreateReferralChain(AppDbContext context, int levels, int startUserId = 1, int packageId = 1)
        {
            var userIds = new List<int>();
            int currentUserId = startUserId;

            // Create the top-level user (no referrer)
            var topUser = CreateUser(currentUserId);
            context.Users.Add(topUser);
            userIds.Add(currentUserId);

            // Create a user package for the top user
            var userPackage = CreateUserPackage(currentUserId, currentUserId, packageId);
            context.UserPackages.Add(userPackage);

            // Create the rest of the chain
            for (int i = 1; i < levels; i++)
            {
                int referrerId = currentUserId;
                currentUserId++;

                var user = CreateUser(currentUserId, $"REF{currentUserId}", referrerId);
                context.Users.Add(user);
                userIds.Add(currentUserId);

                // Create a user package for this user
                userPackage = CreateUserPackage(currentUserId, currentUserId, packageId);
                context.UserPackages.Add(userPackage);
            }

            context.SaveChanges();
            return userIds;
        }
    }
}
