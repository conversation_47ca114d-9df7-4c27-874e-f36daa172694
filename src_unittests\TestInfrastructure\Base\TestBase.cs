using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;
using RazeWinComTr.Tests.Mocks;
using RazeWinComTr.Tests.TestInfrastructure.Factories;
using RazeWinComTr.Tests.TestInfrastructure.Builders;
using RazeWinComTr.Tests.TestInfrastructure.Utilities;

namespace RazeWinComTr.Tests.TestInfrastructure.Base
{
    /// <summary>
    /// Base class for all test classes in the RazeWinComTr.Tests namespace.
    /// Provides common functionality for creating test database contexts, mock services, and loggers.
    /// </summary>
    public abstract class TestBase
    {
        /// <summary>
        /// Creates a new in-memory database context for testing.
        /// Each test should use a unique database name to ensure test isolation.
        /// </summary>
        /// <param name="databaseName">A unique name for the in-memory database</param>
        /// <returns>A new AppDbContext instance connected to an in-memory database</returns>
        protected AppDbContext CreateDbContext(string databaseName)
        {
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName)
                .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
                .Options;

            var context = new AppDbContext(options);
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();
            return context;
        }

        /// <summary>
        /// Creates a mock logger for testing.
        /// </summary>
        /// <typeparam name="T">The type that the logger is for</typeparam>
        /// <returns>A mock ILogger instance</returns>
        protected Mock<ILogger<T>> CreateMockLogger<T>()
        {
            return new Mock<ILogger<T>>();
        }

        /// <summary>
        /// Creates a mock WalletService for testing using the standardized factory.
        /// Prefer using MockServiceFactory.CreateWalletService() directly for new tests.
        /// </summary>
        /// <returns>A Mock&lt;IWalletService&gt; instance</returns>
        protected Mock<IWalletService> CreateMockWalletService()
        {
            return MockServiceFactory.CreateWalletService();
        }

        /// <summary>
        /// Creates a mock WalletService with pre-populated wallets.
        /// </summary>
        /// <param name="initialWallets">Initial wallets to populate</param>
        /// <returns>A Mock&lt;IWalletService&gt; instance</returns>
        protected Mock<IWalletService> CreateMockWalletService(Dictionary<(int UserId, int CoinId), Wallet> initialWallets)
        {
            return MockServiceFactory.CreateWalletService(initialWallets);
        }

        /// <summary>
        /// Legacy implementation - kept for backward compatibility.
        /// Use CreateMockWalletService() for new tests.
        /// </summary>
        [Obsolete("Use CreateMockWalletService() with MockServiceFactory instead")]
        protected Mock<IWalletService> CreateLegacyMockWalletService()
        {
            var mock = new Mock<IWalletService>();
            var wallets = new Dictionary<(int UserId, int CoinId), Wallet>();
            var nextId = 1;

            // Setup GetByIdAsync
            mock.Setup(x => x.GetByIdAsync(It.IsAny<int>()))
                .ReturnsAsync((int id) => wallets.Values.FirstOrDefault(w => w.Id == id));

            // Setup GetByUserIdAsync
            mock.Setup(x => x.GetByUserIdAsync(It.IsAny<int>()))
                .ReturnsAsync((int userId) => wallets.Values.Where(w => w.UserId == userId).ToList());

            // Setup GetTopNByUserIdAsync
            mock.Setup(x => x.GetTopNByUserIdAsync(It.IsAny<int>(), It.IsAny<int>()))
                .ReturnsAsync((int userId, int topN) => wallets.Values
                    .Where(w => w.UserId == userId)
                    .OrderByDescending(w => w.Balance)
                    .Take(topN)
                    .ToList());

            mock.Setup(x => x.GetByUserIdAndCoinIdAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((int userId, int coinId, AppDbContext context) =>
                {
                    wallets.TryGetValue((userId, coinId), out var wallet);
                    return wallet;
                });

            // Setup GetListAsync
            mock.Setup(x => x.GetListAsync())
                .ReturnsAsync(() => wallets.Values.Select(w => new WalletViewModel
                {
                    Id = w.Id,
                    UserId = w.UserId,
                    CoinId = w.CoinId,
                    Balance = w.Balance,
                    CreatedDate = w.CreatedDate,
                    ModifiedDate = w.ModifiedDate
                }).ToList());

            mock.Setup(x => x.CreateAsync(It.IsAny<Wallet>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((Wallet wallet, AppDbContext context) =>
                {
                    wallet.Id = nextId++;
                    wallets[(wallet.UserId, wallet.CoinId)] = wallet;
                    return wallet;
                });

            mock.Setup(x => x.UpdateAsync(It.IsAny<Wallet>(), It.IsAny<AppDbContext>()))
                .Returns((Wallet wallet, AppDbContext context) =>
                {
                    if (wallets.ContainsKey((wallet.UserId, wallet.CoinId)))
                    {
                        wallets[(wallet.UserId, wallet.CoinId)] = wallet;
                    }
                    return Task.CompletedTask;
                });

            // Setup DeleteAsync
            mock.Setup(x => x.DeleteAsync(It.IsAny<int>()))
                .Returns((int id) =>
                {
                    var wallet = wallets.Values.FirstOrDefault(w => w.Id == id);
                    if (wallet != null)
                    {
                        wallets.Remove((wallet.UserId, wallet.CoinId));
                    }
                    return Task.CompletedTask;
                });

            // Setup GetUserAvailableBalanceAsync
            mock.Setup(x => x.GetUserAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<int>()))
                .ReturnsAsync((int userId, int coinId) =>
                {
                    wallets.TryGetValue((userId, coinId), out var wallet);
                    return wallet?.Balance ?? 0;
                });

            // Setup AddAvailableBalanceAsync
            mock.Setup(x => x.AddAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType, AppDbContext context) =>
                {
                    wallets.TryGetValue((userId, rzwTokenInfo.TokenId), out var wallet);

                    if (wallet == null)
                    {
                        wallet = new Wallet
                        {
                            Id = nextId++,
                            UserId = userId,
                            CoinId = rzwTokenInfo.TokenId,
                            Balance = amount,
                            CreatedDate = DateTime.UtcNow
                        };
                        wallets[(userId, rzwTokenInfo.TokenId)] = wallet;
                    }
                    else
                    {
                        wallet.Balance += amount;
                        wallet.ModifiedDate = DateTime.UtcNow;
                    }

                    return wallet;
                });

            // Setup DeductAvailableBalanceAsync
            mock.Setup(x => x.DeductAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((int userId, RzwTokenInfo rzwTokenInfo, decimal amount, AppDbContext context) =>
                {
                    wallets.TryGetValue((userId, rzwTokenInfo.TokenId), out var wallet);

                    if (wallet == null || wallet.Balance < amount)
                    {
                        return false;
                    }

                    wallet.Balance -= amount;
                    wallet.ModifiedDate = DateTime.UtcNow;
                    return true;
                });

            return mock;
        }

        /// <summary>
        /// Creates a mock WalletService that throws exceptions for testing failure scenarios using Moq.
        /// All methods throw "Wallet service failure" exceptions to simulate service failures.
        /// </summary>
        /// <returns>A Mock&lt;IWalletService&gt; instance configured to throw exceptions</returns>
        protected Mock<IWalletService> CreateMockWalletServiceWithFailure()
        {
            var mock = new Mock<IWalletService>();
            var exception = new Exception("Wallet service failure");

            // Setup all methods to throw exceptions
            mock.Setup(x => x.GetByIdAsync(It.IsAny<int>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.GetByUserIdAsync(It.IsAny<int>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.GetTopNByUserIdAsync(It.IsAny<int>(), It.IsAny<int>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.GetByUserIdAndCoinIdAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<AppDbContext>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.GetListAsync())
                .ThrowsAsync(exception);

            mock.Setup(x => x.CreateAsync(It.IsAny<Wallet>(), It.IsAny<AppDbContext>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.UpdateAsync(It.IsAny<Wallet>(), It.IsAny<AppDbContext>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.DeleteAsync(It.IsAny<int>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.GetUserAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<int>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.AddAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>()))
                .ThrowsAsync(exception);

            mock.Setup(x => x.DeductAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<AppDbContext>()))
                .ThrowsAsync(exception);

            return mock;
        }

        /// <summary>
        /// Creates a legacy mock WalletService for backward compatibility.
        /// Use CreateMockWalletService() for new tests.
        /// </summary>
        /// <returns>A MockWalletService instance</returns>
        [Obsolete("Use CreateMockWalletService() with Moq framework instead")]
        protected MockWalletService CreateLegacyMockWalletService()
        {
            return new MockWalletService();
        }

        /// <summary>
        /// Creates a legacy mock WalletService with failure for backward compatibility.
        /// Use CreateMockWalletServiceWithFailure() for new tests.
        /// </summary>
        /// <returns>A MockWalletServiceWithFailure instance</returns>
        [Obsolete("Use CreateMockWalletServiceWithFailure() with Moq framework instead")]
        protected MockWalletServiceWithFailure CreateLegacyMockWalletServiceWithFailure()
        {
            return new MockWalletServiceWithFailure();
        }

        /// <summary>
        /// Creates a mock TokenPriceService for testing using Moq.
        /// The mock is configured to return the specified RZW price for all RZW price-related calls.
        /// </summary>
        /// <param name="buyPrice">The RZW price to return (default: 1.0)</param>
        /// <param name="coinId">The coin to return (default: 1)</param>
        /// <param name="sellPrice">The sell price to return (if null, defaults to buyPrice * 0.95)</param>
        /// <returns>A Mock&lt;ITokenPriceService&gt; instance</returns>
        protected Mock<ITokenPriceService> CreateMockTokenPriceService(decimal buyPrice = 1.0m, int coinId = 1, decimal? sellPrice = null)
        {
            var sellPriceValue = sellPrice ?? buyPrice * 0.95m;
            var mock = new Mock<ITokenPriceService>();

            var rzwTokenInfo = new RzwTokenInfo
            {
                TokenId = coinId,
                BuyPrice = buyPrice,
                SellPrice = sellPriceValue
            };

            mock.Setup(x => x.GetCurrentRzwBuyPriceAsync())
                .ReturnsAsync(buyPrice);

            mock.Setup(x => x.GetRzwTokenIdAsync())
                .ReturnsAsync(coinId);

            mock.Setup(x => x.GetRzwTokenInfoAsync())
                .ReturnsAsync(rzwTokenInfo);

            mock.Setup(x => x.GetCoinInfoAsync(It.IsAny<int>()))
                .ReturnsAsync((int id) => new RzwTokenInfo
                {
                    TokenId = id,
                    BuyPrice = buyPrice,
                    SellPrice = sellPriceValue
                });

            return mock;
        }

        /// <summary>
        /// Creates a mock TradeService for testing using Moq.
        /// The mock is configured with basic setup for CreateAsync method.
        /// </summary>
        /// <returns>A Mock&lt;ITradeService&gt; instance</returns>
        protected Mock<ITradeService> CreateMockTradeService()
        {
            var mock = new Mock<ITradeService>();
            var tradeId = 1;

            // Setup CreateAsync to return the trade with an assigned ID and CreatedDate
            mock.Setup(x => x.CreateAsync(It.IsAny<Trade>()))
                .ReturnsAsync((Trade trade) =>
                {
                    trade.Id = tradeId++;
                    trade.CreatedDate = DateTime.UtcNow;
                    return trade;
                });

            return mock;
        }
    }
}
